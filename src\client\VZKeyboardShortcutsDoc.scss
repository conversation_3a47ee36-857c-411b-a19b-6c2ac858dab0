.vz-settings {
  .keyboard-shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 0;
    padding: 0;
  }

  .shortcut-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #e9ecef;
    }
  }

  .shortcut-keys {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    font-weight: 600;
    color: #212529;

    kbd {
      background-color: #f8f9fa;
      color: #495057;
      border: 1px solid #ced4da;
      border-radius: 4px;
      padding: 4px 8px;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      min-width: 24px;
      text-align: center;
    }

    .or-text {
      color: #6c757d;
      font-size: 14px;
      font-weight: 400;
      margin: 0 4px;
    }
  }

  .shortcut-description {
    color: #495057;
    font-size: 14px;
    line-height: 1.5;
    margin-left: 4px;

    a {
      color: #0d6efd;
      text-decoration: none;

      &:hover {
        color: #0a58ca;
        text-decoration: underline;
      }
    }
  }

  .shortcut-section {
    margin-bottom: 1.5rem;
    
    h6 {
      color: #495057;
      font-weight: 600;
      margin-bottom: 0.75rem;
      margin-top: 1.5rem;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    .shortcut-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 0.75rem 0;
      border-bottom: 1px solid #e9ecef;
      
      &:last-child {
        border-bottom: none;
      }
      
      .shortcut-keys {
        flex: 0 0 auto;
        margin-right: 1rem;
        font-weight: 500;
        
        kbd {
          background-color: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 3px;
          padding: 0.2rem 0.4rem;
          font-size: 0.8rem;
          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
          color: #495057;
          margin: 0 0.1rem;
        }
      }
      
      .shortcut-description {
        flex: 1;
        color: #6c757d;
        line-height: 1.4;
        
        a {
          color: #007bff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
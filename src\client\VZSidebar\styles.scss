.vz-sidebar {
  height: 100%;
  color: var(--vh-color-neutral-04);
  background: var(--vh-color-neutral-01);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .full-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: start;
    border-bottom: 2px solid var(--vh-color-neutral-02);
    overflow: hidden;
  }

  .files {
    margin-top: 6px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: start;
    flex: 1;
    outline: none;
    overflow: auto;
    height: 100%;
  }

  .file-or-directory {
    min-height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    margin: 0 6px;
    padding: 0 4px 0 8px;
    border-radius: 8px;
    font-family: var(--vzcode-font-family);
    font-weight: 500;
  }

  .sidebar-section-hint {
    margin-left: 10px;
    font-size: 12px;
    font-weight: 500;
    font-family: Poppins;
    color: var(--vh-color-neutral-04);
  }

  .sidebar-section-buttons {
    border-right: 2px solid var(--vh-color-neutral-02);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 6px;

    a {
      color: inherit;
    }
  }

  .sidebar-files,
  .sidebar-search {
    width: 100%;
  }

  .sidebar-search {
    * {
      color: inherit;
    }
  }

  .sidebar-search .arrow-wrapper {
    cursor: pointer;
  }

  .sidebar-search-form {
    padding: 10px;
  }

  .search-file-heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    cursor: pointer;

    * {
      margin: 0px;
    }
  }

  .search-file-title,
  .search-file-name,
  .search-file-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }

  .search-file-name {
    font-size: inherit;
  }

  .search-file-count {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    background: var(--vh-color-neutral-04);
    color: var(--vh-color-neutral-01);
    border-radius: 100%;
  }

  .search-state {
    margin: 10px;
  }

  .search-results .active {
    border: 1px solid var(--vh-color-neutral-04);
    background-color: var(--vh-color-neutral-02);
    cursor: pointer;
  }

  .search-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 10px;
    cursor: pointer;
  }

  .search-line p {
    padding-left: 30px;
    margin: 10px 0px;
  }

  .search-file-lines {
    font-size: inherit;
    margin-top: 10px;
  }

  .search-file-lines .search-pattern {
    background: var(--vh-color-neutral-04);
    color: var(--vh-color-neutral-01);
    margin: 0.5px;
    padding: 1px;
  }

  .new-btn {
    justify-content: right;
    margin-right: 10px;
    cursor: pointer;
  }

  .utils {
    display: flex;
    gap: 2px;
  }

  .file-or-directory:hover,
  .settings:hover {
    // background-color: #47546b;
    background-color: rgba(255, 255, 255, 0.075);
  }

  .active-file,
  .active-file:hover {
    background-color: #264399;
  }

  .name {
    display: flex;
    text-overflow: ellipsis;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    gap: 6px;
    .file-icon,
    .arrow-wrapper {
      opacity: 0.5;
    }
  }

  .rename-input {
    width: 100%;
    border: 0px;
    outline: none;
    background: transparent;
    color: var(--vh-color-neutral-04);
    font-family: var(--vzcode-font-family);
    font-size: inherit;
    font-weight: inherit;
    padding: 0px;
    margin: 0px;
  }

  .indentation {
    margin-left: 20px;
  }

  .settings {
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 14px 0;
    margin: 10px;
    border-radius: 4px;
  }

  /* Empty state for when there are no files */
  .empty {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 10px;
    padding: 20px;
    border: 1px dashed rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    min-height: 200px;
    position: relative;
    pointer-events: none;

    .empty-text {
      font-size: 16px;
      font-weight: 500;
      pointer-events: none;
      position: absolute;
      text-align: center;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 80%;
      opacity: 1;
      transition: opacity 0.2s ease;

      &:not(:first-child) {
        opacity: 0;
      }
    }

    &.drag-over {
      .empty-text {
        opacity: 0;

        &:last-child {
          opacity: 1;
        }
      }
    }
  }

  .connection-status {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    font-size: 12px;

    .connection-status-indicator {
      width: 16px;
      height: 16px;
      border-radius: 50%;

      &.connected {
        background-color: var(--vh-color-success-01);
      }

      &.pending {
        background-color: var(--vh-color-caution-01);
      }

      &.disconnected {
        background-color: var(--vh-color-warning-01);
      }
    }
  }
  .name {
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    gap: 6px;
    position: relative;
  }

  .presence-indicators {
    position: absolute;
    top: 0;
    display: flex;
    gap: 4px;
  }

  .presence-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: #000000;
    z-index: 1;
  }
}

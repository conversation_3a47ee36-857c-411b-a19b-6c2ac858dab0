.vz-tab-list {
  display: flex;
  background-color: #1f2022;
  // When the tab list becomes too large, a horizontal scroll bar appears.
  overflow-x: auto;

  .tab {
    // background: #47546b;
    color: #fff;
    font-family: var(--vzcode-font-family);
    font-weight: 500;
    align-items: center;
    display: flex;
    gap: 6px;
    padding: 4px 4px 4px 14px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin-top: 6px;
    margin-left: 2px;
    margin-right: 2px;
    cursor: pointer;
    // Make sure text doesn't wrap within tabs.
    white-space: nowrap;

    // No left margin for first child
    &:first-child {
      margin-left: 0;
    }

    // on hover
    &:hover {
      // background-color: var(--vh-color-hover-dark);
      background-color: rgba(255, 255, 255, 0.075);
    }
  }

  .tab.active {
    background-color: #202e46;
  }

  .tab.transient {
    font-style: italic;
  }

  .icon-button {
    color: var(--vh-color-neutral-03);
  }
  .icon-button:hover {
    color: var(--vh-color-action-03);
  }
}

<!DOCTYPE html>
<html>
  <head>
    <title>React Counter App</title>
    <script type="importmap">
      {
        "imports": {
          "react": "https://cdn.jsdelivr.net/npm/react@19.1.0/+esm",
          "react/jsx-runtime": "https://cdn.jsdelivr.net/npm/react@19.1.0/jsx-runtime/+esm",
          "react-dom/client": "https://cdn.jsdelivr.net/npm/react-dom@19.1.0/client/+esm"
        }
      }
    </script>
    <script src="https://unpkg.com/@tailwindcss/browser@4"></script>
    <style type="text/tailwindcss">
      @theme {
        --color-primary: #3b82f6;
        --color-secondary: #6366f1;
        --color-accent: #f59e0b;
        --color-danger: #d44040;
        --color-success: #10b981;
      }

      .text-primary {
        color: theme(--color-primary);
      }
      .text-secondary {
        color: theme(--color-secondary);
      }
      .bg-primary {
        background-color: theme(--color-primary);
      }
      .bg-secondary {
        background-color: theme(--color-secondary);
      }
      .bg-accent {
        background-color: theme(--color-accent);
      }
      .bg-danger {
        background-color: theme(--color-danger);
      }
      .bg-success {
        background-color: theme(--color-success);
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="index.jsx"></script>
  </body>
</html>

{"name": "vzcode", "version": "1.31.0", "description": "Multiplayer code editor system", "main": "src/index.ts", "type": "module", "bin": "src/server/index.js", "files": ["dist/**", "src/**"], "scripts": {"test": "vitest run", "test-interactive": "cd test/sampleDirectories/kitchenSink && node ../../../src/server/index.js", "prettier": "prettier {*.*,**/*.*} --write", "typecheck": "tsc --noEmit", "dev": "cross-env EDITOR_PORT=5173 concurrently \"npm run test-interactive\" \"vite\"", "build": "vite build", "build-release": "vite build --mode release", "preview": "concurrently \"npm run test-interactive\" \"vite preview\"", "prepublishOnly": "npm run build-release", "upgrade": "ncu -x react,react-dom,@types/react,@types/react-dom -u"}, "repository": {"type": "git", "url": "git+https://github.com/vizhub-core/vzcode.git"}, "keywords": ["Code", "Editor", "CodeMirror", "Collaboration", "Development", "IDE", "JavaScript", "Data", "Visualization"], "contributors": [{"name": "<PERSON>", "url": "https://github.com/curran"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Anooj-Pai"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>k"}, {"name": "<PERSON>", "url": "https://github.com/aruan20"}, {"name": "<PERSON>", "url": "https://github.com/EvWhymark"}, {"name": "<PERSON>", "url": "https://github.com/enuessle"}, {"name": "<PERSON>", "url": "https://github.com/MandingoBrandon"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/demhos"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/CJary"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/viratkohli2011"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/NghiaMinhHuynh"}, {"name": "<PERSON>", "url": "https://github.com/michaelhelper"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/DaCatDude"}, {"name": "<PERSON>", "url": "https://github.com/philippark"}, {"name": "<PERSON>", "url": "https://github.com/jeffrey-asm"}], "license": "MIT", "bugs": {"url": "https://github.com/vizhub-core/vzcode/issues"}, "homepage": "https://github.com/vizhub-core/vzcode#readme", "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lint": "^6.8.5", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.37.1", "@langchain/core": "^0.3.57", "@langchain/openai": "^0.5.12", "@lezer/highlight": "^1.2.1", "@livekit/components-react": "^2.9.9", "@replit/codemirror-indentation-markers": "^6.5.3", "@replit/codemirror-interact": "^6.3.1", "@replit/codemirror-lang-svelte": "^6.0.0", "@replit/codemirror-vscode-keymap": "^6.0.2", "@teamwork/websocket-json-stream": "^2.0.0", "@typescript/vfs": "^1.6.1", "@uiw/codemirror-theme-abcdef": "^4.23.12", "@uiw/codemirror-theme-dracula": "^4.23.12", "@uiw/codemirror-theme-eclipse": "^4.23.12", "@uiw/codemirror-theme-github": "^4.23.12", "@uiw/codemirror-theme-material": "^4.23.12", "@uiw/codemirror-theme-nord": "^4.23.12", "@uiw/codemirror-theme-okaidia": "^4.23.12", "@uiw/codemirror-theme-xcode": "^4.23.12", "@uiw/codemirror-themes": "^4.23.12", "@valtown/codemirror-ts": "^2.3.1", "@vizhub/viz-types": "^0.1.0", "body-parser": "^2.2.0", "codemirror": "^6.0.1", "codemirror-copilot": "^0.0.7", "codemirror-ot": "^4.4.0", "color-hash": "^2.0.2", "comlink": "^4.4.2", "d3-array": "^3.2.4", "diff-match-patch": "^1.0.5", "dotenv": "^16.5.0", "eslint": "^9.28.0", "eslint-linter-browserify": "^9.28.0", "express": "^5.1.0", "globals": "^16.2.0", "ignore": "^7.0.5", "json0-ot-diff": "^1.1.2", "livekit-server-sdk": "^2.13.0", "ngrok": "^5.0.0-beta.2", "npm": "^11.4.1", "open": "^10.1.2", "prettier-plugin-svelte": "^3.4.0", "react": "^18", "react-bootstrap": "^2.10.10", "react-dom": "^18", "react-router-dom": "^7.6.2", "sharedb": "^5.2.2", "sharedb-client-browser": "^5.2.2", "uuid": "^11.1.0", "vizhub-ui": "^3.23.0", "ws": "^8.18.2"}, "devDependencies": {"@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.5.1", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "npm-check-updates": "^18.0.1", "prettier": "^3.5.3", "sass": "^1.89.1", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.2"}, "optionalDependencies": {"@rollup/rollup-darwin-arm64": "^4.42.0", "@rollup/rollup-win32-x64-msvc": "^4.42.0"}}
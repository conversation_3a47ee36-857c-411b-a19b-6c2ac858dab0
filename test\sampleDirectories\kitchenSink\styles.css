html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
  font-family: 'Inter', sans-serif;
}

#viz-container,
#root {
  width: 100%;
  height: 100%;
}

#viz-container {
  position: relative;
}

#viz-container svg {
  position: absolute;
}

.title-text {
  font-family: 'Inter', sans-serif;
  font-size: 40px;
  font-weight: 600;
  fill: #333333;
  user-select: none;
  pointer-events: none;
}

.diagonal-line {
  stroke: #f2f2f2;
  stroke-width: 32;
}

.bar {
  transition:
    fill 0.3s,
    opacity 0.3s;
}

.bar:hover {
  fill: #3d5c57;
}

.axis text {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  fill: #666666;
}

.axis line, .axis path {
  stroke: #cccccc;
  stroke-width: 1;
}

.axis-title {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  fill: #555555;
}